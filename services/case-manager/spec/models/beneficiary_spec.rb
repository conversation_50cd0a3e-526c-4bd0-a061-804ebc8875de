require 'rails_helper'

RSpec.describe Beneficiary, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:gender) }
    it { should validate_presence_of(:nationality) }
    it { should validate_presence_of(:city) }
    it { should validate_presence_of(:date_of_birth) }
    it { should validate_presence_of(:project_id) }
    it { should validate_inclusion_of(:gender).in_array(%w[male female other]) }
    it { should validate_length_of(:name).is_at_least(2).is_at_most(100) }
  end

  describe 'associations' do
    it { should have_many(:cases).dependent(:destroy) }
    it { should have_many(:case_managers).through(:cases) }
    it { should have_many(:case_plans).through(:cases) }
  end

  describe 'scopes' do
    let!(:adult_beneficiary) { create(:beneficiary, :adult, project_id: 1) }
    let!(:minor_beneficiary) { create(:beneficiary, :minor, project_id: 1) }

    it 'filters by age correctly' do
      expect(Beneficiary.adults).to include(adult_beneficiary)
      expect(Beneficiary.minors).to include(minor_beneficiary)
    end
  end

  describe 'project-based filtering' do
    let!(:project1_beneficiary) { create(:beneficiary, project_id: 1) }
    let!(:project2_beneficiary) { create(:beneficiary, project_id: 2) }

    it 'can be filtered by project_id' do
      expect(Beneficiary.where(project_id: 1)).to include(project1_beneficiary)
      expect(Beneficiary.where(project_id: 1)).not_to include(project2_beneficiary)
    end
  end

  describe 'instance methods' do
    let(:beneficiary) { create(:beneficiary, date_of_birth: 25.years.ago, project_id: 1) }

    it 'calculates age correctly' do
      expect(beneficiary.age).to eq(25)
    end

    it 'identifies adults correctly' do
      expect(beneficiary.is_adult?).to be true
      expect(beneficiary.is_minor?).to be false
    end
  end
end
