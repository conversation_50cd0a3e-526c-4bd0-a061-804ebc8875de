require 'rails_helper'

RSpec.describe Case, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:beneficiary_id) }
    it { should validate_presence_of(:case_manager_id) }
    it { should validate_presence_of(:project_id) }
    it { should validate_inclusion_of(:status).in_array(%w[open in_progress closed suspended]) }
    it { should validate_inclusion_of(:approval_status).in_array(%w[pending approved rejected]).allow_blank }
  end

  describe 'associations' do
    it { should belong_to(:beneficiary) }
    it { should belong_to(:case_manager) }
    it { should have_many(:case_plans).dependent(:destroy) }
    it { should have_many(:services).through(:case_plans) }
  end

  describe 'scopes' do
    let!(:open_case) { create(:case, :open, project_id: 1) }
    let!(:in_progress_case) { create(:case, :in_progress, project_id: 1) }
    let!(:closed_case) { create(:case, :closed, project_id: 1) }
    let!(:suspended_case) { create(:case, :suspended, project_id: 1) }

    it 'filters by status correctly' do
      expect(Case.open).to include(open_case)
      expect(Case.in_progress).to include(in_progress_case)
      expect(Case.closed).to include(closed_case)
      expect(Case.suspended).to include(suspended_case)
      expect(Case.active).to include(open_case, in_progress_case)
    end
  end

  describe 'project-based filtering' do
    let!(:project1_case) { create(:case, project_id: 1) }
    let!(:project2_case) { create(:case, project_id: 2) }

    it 'can be filtered by project_id' do
      expect(Case.where(project_id: 1)).to include(project1_case)
      expect(Case.where(project_id: 1)).not_to include(project2_case)
    end
  end

  describe 'instance methods' do
    let(:case_record) { create(:case, :open, project_id: 1) }

    it 'correctly identifies active cases' do
      expect(case_record.is_active?).to be true
    end

    it 'correctly identifies closed cases' do
      closed_case = create(:case, :closed, project_id: 1)
      expect(closed_case.is_closed?).to be true
    end

    it 'calculates duration in days' do
      case_record.update(started_at: 10.days.ago)
      expect(case_record.duration_in_days).to eq(10)
    end
  end
end
