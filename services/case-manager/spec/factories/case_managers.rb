FactoryBot.define do
  factory :case_manager do
    user_id { SecureRandom.uuid }
    supervisor_id { nil }
    project_id { 1 }

    trait :with_supervisor do
      supervisor_id { create(:case_manager).id }
    end

    trait :supervisor do
      after(:create) do |case_manager|
        create_list(:case_manager, 2, supervisor: case_manager)
      end
    end

    trait :with_cases do
      after(:create) do |case_manager|
        create_list(:case, 3, case_manager: case_manager)
      end
    end

    trait :active do
      after(:create) do |case_manager|
        create_list(:case, 2, case_manager: case_manager, status: 'open')
        create_list(:case, 1, case_manager: case_manager, status: 'in_progress')
      end
    end
  end
end
