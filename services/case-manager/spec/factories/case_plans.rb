FactoryBot.define do
  factory :case_plan do
    association :case
    status { "draft" }
    summary { Faker::Lorem.paragraph(sentence_count: 5) }
    approved { false }
    approved_at { nil }

    trait :draft do
      status { "draft" }
      approved { false }
      approved_at { nil }
    end

    trait :active do
      status { "active" }
      approved { true }
      approved_at { Faker::Time.between(from: 1.month.ago, to: Time.current) }
    end

    trait :completed do
      status { "completed" }
      approved { true }
      approved_at { Faker::Time.between(from: 2.months.ago, to: 1.month.ago) }
    end

    trait :cancelled do
      status { "cancelled" }
      approved { false }
      approved_at { nil }
    end

    trait :approved do
      approved { true }
      approved_at { Faker::Time.between(from: 1.week.ago, to: Time.current) }
    end

    trait :with_services do
      after(:create) do |case_plan|
        create_list(:service, 3, case_plan: case_plan)
      end
    end

    trait :comprehensive do
      summary { Faker::Lorem.paragraph(sentence_count: 10) }
      after(:create) do |case_plan|
        create_list(:service, 5, case_plan: case_plan)
      end
    end
  end
end
