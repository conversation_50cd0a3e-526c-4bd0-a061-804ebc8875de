FactoryBot.define do
  factory :case do
    association :beneficiary
    association :case_manager
    project_id { 1 }
    status { "open" }
    approval_status { nil }
    started_at { Faker::Time.between(from: 6.months.ago, to: Time.current) }
    closed_at { nil }

    trait :open do
      status { "open" }
      closed_at { nil }
    end

    trait :in_progress do
      status { "in_progress" }
      closed_at { nil }
    end

    trait :closed do
      status { "closed" }
      closed_at { Faker::Time.between(from: started_at, to: Time.current) }
    end

    trait :suspended do
      status { "suspended" }
      closed_at { nil }
    end

    trait :pending_approval do
      approval_status { "pending" }
    end

    trait :approved do
      approval_status { "approved" }
    end

    trait :rejected do
      approval_status { "rejected" }
    end

    trait :with_case_plans do
      after(:create) do |case_record|
        create_list(:case_plan, 2, case: case_record)
      end
    end

    trait :complete_workflow do
      after(:create) do |case_record|
        case_plan = create(:case_plan, case: case_record, status: "active", approved: true)
        create_list(:service, 3, case_plan: case_plan)
      end
    end
  end
end
