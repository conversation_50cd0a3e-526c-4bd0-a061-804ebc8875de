FactoryBot.define do
  factory :beneficiary do
    name { Faker::Name.name }
    gender { %w[male female other].sample }
    nationality { Faker::Nation.nationality }
    city { Faker::Address.city }
    date_of_birth { Faker::Date.birthday(min_age: 1, max_age: 80) }
    project_id { 1 }

    trait :minor do
      date_of_birth { Faker::Date.birthday(min_age: 1, max_age: 17) }
    end

    trait :adult do
      date_of_birth { Faker::Date.birthday(min_age: 18, max_age: 80) }
    end

    trait :male do
      gender { 'male' }
      name { Faker::Name.male_first_name + ' ' + Faker::Name.last_name }
    end

    trait :female do
      gender { 'female' }
      name { Faker::Name.female_first_name + ' ' + Faker::Name.last_name }
    end

    trait :with_cases do
      after(:create) do |beneficiary|
        create_list(:case, 2, beneficiary: beneficiary)
      end
    end
  end
end
