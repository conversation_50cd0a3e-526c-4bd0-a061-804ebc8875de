require 'rails_helper'

RSpec.describe "Api::Beneficiaries", type: :request do
  let(:auth_data) { create_authenticated_case_manager }
  let(:user) { auth_data[:user] }
  let(:project) { auth_data[:project] }
  let(:case_manager) { auth_data[:case_manager] }
  let(:valid_headers) { authenticated_headers(user: user, project: project) }

  let(:valid_attributes) do
    {
      name: "<PERSON>",
      gender: "male",
      nationality: "Jordanian",
      city: "Amman",
      date_of_birth: "1990-01-01"
    }
  end

  let(:invalid_attributes) do
    {
      name: "",
      gender: "invalid",
      nationality: "",
      city: "",
      date_of_birth: Date.current + 1.day
    }
  end

  before do
    # Mock collection loading for index actions
    mock_collection_loading(Beneficiary, [])
    # Mock instance loading for show/update/destroy actions
    mock_instance_loading(Beneficiary, build(:beneficiary, project_id: project.id))
  end

  describe "GET /api/beneficiaries" do
    it "returns a successful response with JSON:API format" do
      create_list(:beneficiary, 3)

      get "/api/beneficiaries", headers: valid_headers, as: :json

      expect(response).to be_successful
      expect_json_api_response
      expect(json_response['data']).to be_an(Array)
      expect(json_response['data'].length).to eq(3)
    end

    it "supports pagination" do
      create_list(:beneficiary, 25)

      get "/api/beneficiaries?page[number]=1&page[size]=10", headers: valid_headers, as: :json

      expect(response).to be_successful
      expect(json_response['data'].length).to eq(10)
      expect(json_response['meta']).to have_key('pagination')
    end

    it "supports filtering by gender" do
      create(:beneficiary, gender: 'male')
      create(:beneficiary, gender: 'female')

      get "/api/beneficiaries?filter[gender]=male", headers: valid_headers, as: :json

      expect(response).to be_successful
      expect(json_response['data'].length).to eq(1)
      expect(json_response['data'][0]['attributes']['gender']).to eq('male')
    end
  end

  describe "GET /api/beneficiaries/:id" do
    it "returns the beneficiary details" do
      beneficiary = create(:beneficiary)

      get "/api/beneficiaries/#{beneficiary.id}", headers: valid_headers, as: :json

      expect(response).to be_successful
      expect_json_api_response
      expect(json_response['data']['id']).to eq(beneficiary.id.to_s)
      expect(json_response['data']['attributes']['name']).to eq(beneficiary.name)
    end

    it "returns 404 for non-existent beneficiary" do
      get "/api/beneficiaries/999999", headers: valid_headers, as: :json

      expect(response).to have_http_status(:not_found)
      expect_json_api_error_response
    end
  end

  describe "POST /api/beneficiaries" do
    context "with valid parameters" do
      it "creates a new beneficiary" do
        expect {
          post "/api/beneficiaries",
               params: { beneficiary: valid_attributes }.to_json,
               headers: valid_headers
        }.to change(Beneficiary, :count).by(1)
      end

      it "returns the created beneficiary with JSON:API format" do
        post "/api/beneficiaries",
             params: { beneficiary: valid_attributes }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:created)
        expect_json_api_response
        expect(json_response['data']['attributes']['name']).to eq(valid_attributes[:name])
        expect(json_response['data']['attributes']['gender']).to eq(valid_attributes[:gender])
      end

      it "includes computed attributes" do
        post "/api/beneficiaries",
             params: { beneficiary: valid_attributes }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:created)
        expect(json_response['data']['attributes']).to have_key('age')
        expect(json_response['data']['attributes']).to have_key('is_minor')
        expect(json_response['data']['attributes']).to have_key('is_adult')
      end
    end

    context "with invalid parameters" do
      it "does not create a new beneficiary" do
        expect {
          post "/api/beneficiaries",
               params: { beneficiary: invalid_attributes }.to_json,
               headers: valid_headers
        }.to change(Beneficiary, :count).by(0)
      end

      it "returns validation errors" do
        post "/api/beneficiaries",
             params: { beneficiary: invalid_attributes }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect_json_api_error_response
        expect(json_response['errors']).to be_present
      end
    end

    context "without authentication" do
      it "returns unauthorized" do
        post "/api/beneficiaries",
             params: { beneficiary: valid_attributes }.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "PATCH /api/beneficiaries/:id" do
    let(:beneficiary) { create(:beneficiary) }
    let(:new_attributes) do
      {
        name: "Updated Name",
        city: "Updated City"
      }
    end

    context "with valid parameters" do
      it "updates the beneficiary" do
        patch "/api/beneficiaries/#{beneficiary.id}",
              params: { beneficiary: new_attributes }.to_json,
              headers: valid_headers

        beneficiary.reload
        expect(beneficiary.name).to eq("Updated Name")
        expect(beneficiary.city).to eq("Updated City")
      end

      it "returns the updated beneficiary" do
        patch "/api/beneficiaries/#{beneficiary.id}",
              params: { beneficiary: new_attributes }.to_json,
              headers: valid_headers

        expect(response).to have_http_status(:ok)
        expect_json_api_response
        expect(json_response['data']['attributes']['name']).to eq("Updated Name")
      end
    end

    context "with invalid parameters" do
      it "returns validation errors" do
        patch "/api/beneficiaries/#{beneficiary.id}",
              params: { beneficiary: invalid_attributes }.to_json,
              headers: valid_headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect_json_api_error_response
      end
    end
  end

  describe "DELETE /api/beneficiaries/:id" do
    it "destroys the beneficiary" do
      beneficiary = create(:beneficiary)

      expect {
        delete "/api/beneficiaries/#{beneficiary.id}", headers: valid_headers
      }.to change(Beneficiary, :count).by(-1)
    end

    it "returns no content status" do
      beneficiary = create(:beneficiary)

      delete "/api/beneficiaries/#{beneficiary.id}", headers: valid_headers

      expect(response).to have_http_status(:no_content)
    end
  end
end
