require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by <PERSON>s when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/services", type: :request do
  # This should return the minimal set of attributes required to create a valid
  # Service. As you add validations to Service, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  # This should return the minimal set of values that should be in the headers
  # in order to pass any filters (e.g. authentication) defined in
  # ServicesController, or in your router and rack
  # middleware. Be sure to keep this updated too.
  let(:valid_headers) {
    {}
  }

  describe "GET /index" do
    it "renders a successful response" do
      Service.create! valid_attributes
      get services_url, headers: valid_headers, as: :json
      expect(response).to be_successful
    end
  end

  describe "GET /show" do
    it "renders a successful response" do
      service = Service.create! valid_attributes
      get service_url(service), as: :json
      expect(response).to be_successful
    end
  end

  describe "POST /create" do
    context "with valid parameters" do
      it "creates a new Service" do
        expect {
          post services_url,
               params: { service: valid_attributes }, headers: valid_headers, as: :json
        }.to change(Service, :count).by(1)
      end

      it "renders a JSON response with the new service" do
        post services_url,
             params: { service: valid_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:created)
        expect(response.content_type).to match(a_string_including("application/json"))
      end
    end

    context "with invalid parameters" do
      it "does not create a new Service" do
        expect {
          post services_url,
               params: { service: invalid_attributes }, as: :json
        }.to change(Service, :count).by(0)
      end

      it "renders a JSON response with errors for the new service" do
        post services_url,
             params: { service: invalid_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to match(a_string_including("application/json"))
      end
    end
  end

  describe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested service" do
        service = Service.create! valid_attributes
        patch service_url(service),
              params: { service: new_attributes }, headers: valid_headers, as: :json
        service.reload
        skip("Add assertions for updated state")
      end

      it "renders a JSON response with the service" do
        service = Service.create! valid_attributes
        patch service_url(service),
              params: { service: new_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to match(a_string_including("application/json"))
      end
    end

    context "with invalid parameters" do
      it "renders a JSON response with errors for the service" do
        service = Service.create! valid_attributes
        patch service_url(service),
              params: { service: invalid_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to match(a_string_including("application/json"))
      end
    end
  end

  describe "DELETE /destroy" do
    it "destroys the requested service" do
      service = Service.create! valid_attributes
      expect {
        delete service_url(service), headers: valid_headers, as: :json
      }.to change(Service, :count).by(-1)
    end
  end
end
