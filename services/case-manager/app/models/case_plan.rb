class CasePlan < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :case
  has_many :services, dependent: :destroy
  has_one :beneficiary, through: :case
  has_one :case_manager, through: :case

  # Validations
  validates :status, presence: true, inclusion: { in: %w[draft active completed cancelled] }
  validates :summary, presence: true, length: { minimum: 10, maximum: 2000 }
  validates :case_id, presence: true
  validate :approved_at_consistency

  # Callbacks
  before_validation :set_default_status, on: :create
  after_update :set_approved_at, if: :saved_change_to_approved?

  # Scopes
  scope :draft, -> { where(status: "draft") }
  scope :active, -> { where(status: "active") }
  scope :completed, -> { where(status: "completed") }
  scope :cancelled, -> { where(status: "cancelled") }
  scope :approved, -> { where(approved: true) }
  scope :pending_approval, -> { where(approved: [ false, nil ]) }
  scope :by_case, ->(case_id) { where(case_id: case_id) if case_id.present? }
  scope :recent, -> { order(created_at: :desc) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[status summary approved approved_at created_at updated_at case_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[case services beneficiary case_manager]
  end

  # Instance methods
  def is_draft?
    status == "draft"
  end

  def is_active?
    status == "active"
  end

  def is_completed?
    status == "completed"
  end

  def is_cancelled?
    status == "cancelled"
  end

  def is_approved?
    approved == true
  end

  def is_pending_approval?
    !is_approved?
  end

  def can_be_activated?
    is_draft? && is_approved?
  end

  def can_be_completed?
    is_active? && services.where.not(status: "completed").empty?
  end

  def activate!
    return false unless can_be_activated?

    update!(status: "active")
  end

  def complete!
    return false unless can_be_completed?

    update!(status: "completed")
  end

  def cancel!
    return false if is_completed?

    update!(status: "cancelled")
  end

  def approve!
    update!(approved: true, approved_at: Time.current)
  end

  def reject!
    update!(approved: false, approved_at: nil)
  end

  def services_count
    services.count
  end

  def completed_services_count
    services.where(status: "completed").count
  end

  def progress_percentage
    return 0 if services_count.zero?

    (completed_services_count.to_f / services_count * 100).round(2)
  end

  def plan_summary
    {
      id: id,
      case_id: case_id,
      status: status,
      approved: approved,
      services_count: services_count,
      completed_services_count: completed_services_count,
      progress_percentage: progress_percentage,
      beneficiary_name: beneficiary&.name,
      case_manager_name: case_manager&.name
    }
  end

  private

  def set_default_status
    self.status ||= "draft"
  end

  def set_approved_at
    if approved?
      self.approved_at ||= Time.current
    else
      self.approved_at = nil
    end
  end

  def approved_at_consistency
    if approved? && approved_at.blank?
      errors.add(:approved_at, "must be set when plan is approved")
    elsif !approved? && approved_at.present?
      errors.add(:approved_at, "must be blank when plan is not approved")
    end
  end
end
