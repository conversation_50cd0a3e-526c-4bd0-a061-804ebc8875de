class Beneficiary < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  has_many :cases, dependent: :destroy
  has_many :case_managers, through: :cases
  has_many :case_plans, through: :cases

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :gender, presence: true, inclusion: { in: %w[male female other] }
  validates :nationality, presence: true, length: { minimum: 2, maximum: 50 }
  validates :city, presence: true, length: { minimum: 2, maximum: 50 }
  validates :date_of_birth, presence: true
  validates :project_id, presence: true
  validate :date_of_birth_not_in_future
  validate :reasonable_age

  # Scopes
  scope :adults, -> { where("date_of_birth <= ?", 18.years.ago) }
  scope :minors, -> { where("date_of_birth > ?", 18.years.ago) }
  scope :by_gender, ->(gender) { where(gender: gender) if gender.present? }
  scope :by_nationality, ->(nationality) { where(nationality: nationality) if nationality.present? }
  scope :by_city, ->(city) { where(city: city) if city.present? }
  scope :active_cases, -> { joins(:cases).where(cases: { status: [ "open", "in_progress" ] }).distinct }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name gender nationality city date_of_birth project_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[cases case_managers case_plans]
  end

  # Instance methods
  def age
    return nil unless date_of_birth

    ((Date.current - date_of_birth) / 365.25).floor
  end

  def is_minor?
    age && age < 18
  end

  def is_adult?
    age && age >= 18
  end

  def active_cases
    cases.where(status: [ "open", "in_progress" ])
  end

  def active_cases_count
    active_cases.count
  end

  def total_cases_count
    cases.count
  end

  def current_case_manager
    active_cases.joins(:case_manager).first&.case_manager
  end

  def full_profile
    {
      name: name,
      age: age,
      gender: gender,
      nationality: nationality,
      city: city,
      is_minor: is_minor?,
      active_cases_count: active_cases_count,
      total_cases_count: total_cases_count
    }
  end

  private

  def date_of_birth_not_in_future
    return unless date_of_birth

    if date_of_birth > Date.current
      errors.add(:date_of_birth, "cannot be in the future")
    end
  end

  def reasonable_age
    return unless date_of_birth

    age_in_years = age
    if age_in_years && age_in_years > 120
      errors.add(:date_of_birth, "indicates an unreasonable age")
    end
  end
end
