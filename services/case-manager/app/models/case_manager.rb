class CaseManager < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include AtharRpc::ActiveRpc

  # Associations
  has_many :cases, dependent: :destroy
  has_many :supervised_case_managers, class_name: "<PERSON><PERSON><PERSON><PERSON>", foreign_key: "supervisor_id", dependent: :nullify
  belongs_to :supervisor, class_name: "<PERSON><PERSON><PERSON><PERSON>", optional: true

  # Through associations
  has_many :beneficiaries, through: :cases
  has_many :case_plans, through: :cases

  # Validations
  validates :user_id, presence: true, uniqueness: true
  validates :supervisor_id, presence: true, if: :requires_supervisor?
  validates :project_id, presence: true

  # Configure the gRPC integration
  active_rpc :core, :User, foreign_key: :user_id do
    attribute :name, :string, default: "Case Manager"
    attribute :email, :string, default: "<EMAIL>"
    attribute :phone, :string, default: "+962000000000"
    attribute :roles, :json, default: []
    attribute :permissions, :json, default: []
  end

  # Scopes
  scope :active, -> { joins(:cases).where(cases: { status: [ "open", "in_progress" ] }).distinct }
  scope :supervisors, -> { where(id: CaseManager.select(:supervisor_id).distinct) }
  scope :without_supervisor, -> { where(supervisor_id: nil) }

  # Ransack configuration
  ransack_alias :search, :user_data_name_or_user_data_email

  def self.ransackable_attributes(auth_object = nil)
    %w[user_id supervisor_id project_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[cases beneficiaries case_plans supervisor supervised_case_managers]
  end

  # Instance methods
  def name
    user_data&.name || "Unknown Case Manager"
  end

  def email
    user_data&.email || "<EMAIL>"
  end

  def phone
    user_data&.phone || "+962000000000"
  end

  def active_cases_count
    cases.where(status: [ "open", "in_progress" ]).count
  end

  def total_cases_count
    cases.count
  end

  def is_supervisor?
    supervised_case_managers.exists?
  end

  private

  def requires_supervisor?
    # Add business logic here - for example, junior case managers might require supervisors
    false
  end
end
