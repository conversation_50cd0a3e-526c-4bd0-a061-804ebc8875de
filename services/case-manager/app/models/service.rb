class Service < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :case_plan
  has_one :case, through: :case_plan
  has_one :beneficiary, through: :case
  has_one :case_manager, through: :case

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :description, presence: true, length: { minimum: 10, maximum: 1000 }
  validates :category, presence: true, inclusion: { in: %w[health education legal social financial housing employment] }
  validates :provider_type, presence: true, inclusion: { in: %w[internal external partner] }
  validates :case_plan_id, presence: true
  validates :project_id, presence: true

  # Callbacks
  before_validation :set_default_status, on: :create

  # Scopes
  scope :by_category, ->(category) { where(category: category) if category.present? }
  scope :by_provider_type, ->(provider_type) { where(provider_type: provider_type) if provider_type.present? }
  scope :internal, -> { where(provider_type: "internal") }
  scope :external, -> { where(provider_type: "external") }
  scope :partner, -> { where(provider_type: "partner") }
  scope :health_services, -> { where(category: "health") }
  scope :education_services, -> { where(category: "education") }
  scope :legal_services, -> { where(category: "legal") }
  scope :social_services, -> { where(category: "social") }
  scope :financial_services, -> { where(category: "financial") }
  scope :recent, -> { order(created_at: :desc) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name description category provider_type project_id created_at updated_at case_plan_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[case_plan case beneficiary case_manager]
  end

  # Instance methods
  def is_internal?
    provider_type == "internal"
  end

  def is_external?
    provider_type == "external"
  end

  def is_partner?
    provider_type == "partner"
  end

  def is_health_service?
    category == "health"
  end

  def is_education_service?
    category == "education"
  end

  def is_legal_service?
    category == "legal"
  end

  def is_social_service?
    category == "social"
  end

  def is_financial_service?
    category == "financial"
  end

  def is_housing_service?
    category == "housing"
  end

  def is_employment_service?
    category == "employment"
  end

  def service_summary
    {
      id: id,
      name: name,
      category: category,
      provider_type: provider_type,
      case_plan_id: case_plan_id,
      beneficiary_name: beneficiary&.name,
      case_manager_name: case_manager&.name
    }
  end

  def full_details
    {
      id: id,
      name: name,
      description: description,
      category: category,
      provider_type: provider_type,
      case_plan_status: case_plan&.status,
      case_status: case_plan&.case&.status,
      beneficiary_name: beneficiary&.name,
      case_manager_name: case_manager&.name,
      created_at: created_at,
      updated_at: updated_at
    }
  end

  # Class methods for statistics
  def self.category_counts
    group(:category).count
  end

  def self.provider_type_counts
    group(:provider_type).count
  end

  def self.most_common_category
    category_counts.max_by { |_, count| count }&.first
  end

  def self.most_common_provider_type
    provider_type_counts.max_by { |_, count| count }&.first
  end

  private

  def set_default_status
    # Services don't have a status field in the current schema
    # This is a placeholder for future enhancement
  end
end
