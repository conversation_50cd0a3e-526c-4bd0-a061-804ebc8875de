class Case < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :beneficiary
  belongs_to :case_manager
  has_many :case_plans, dependent: :destroy
  has_many :services, through: :case_plans

  # Validations
  validates :status, presence: true, inclusion: { in: %w[open in_progress closed suspended] }
  validates :approval_status, inclusion: { in: %w[pending approved rejected] }, allow_blank: true
  validates :beneficiary_id, presence: true
  validates :case_manager_id, presence: true
  validates :project_id, presence: true
  validate :started_at_not_in_future
  validate :closed_at_after_started_at
  validate :closed_at_required_for_closed_status

  # Callbacks
  before_validation :set_default_status, on: :create
  before_validation :set_started_at, on: :create
  after_update :set_closed_at, if: :saved_change_to_status?

  # Scopes
  scope :open, -> { where(status: "open") }
  scope :in_progress, -> { where(status: "in_progress") }
  scope :closed, -> { where(status: "closed") }
  scope :suspended, -> { where(status: "suspended") }
  scope :active, -> { where(status: [ "open", "in_progress" ]) }
  scope :pending_approval, -> { where(approval_status: "pending") }
  scope :approved, -> { where(approval_status: "approved") }
  scope :rejected, -> { where(approval_status: "rejected") }
  scope :by_case_manager, ->(case_manager_id) { where(case_manager_id: case_manager_id) if case_manager_id.present? }
  scope :recent, -> { order(created_at: :desc) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[status approval_status started_at closed_at created_at updated_at beneficiary_id case_manager_id project_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[beneficiary case_manager case_plans services]
  end

  # Instance methods
  def duration_in_days
    return nil unless started_at

    end_date = closed_at || Date.current
    (end_date.to_date - started_at.to_date).to_i
  end

  def is_active?
    %w[open in_progress].include?(status)
  end

  def is_closed?
    status == "closed"
  end

  def is_suspended?
    status == "suspended"
  end

  def requires_approval?
    approval_status == "pending"
  end

  def is_approved?
    approval_status == "approved"
  end

  def is_rejected?
    approval_status == "rejected"
  end

  def active_case_plans
    case_plans.where(status: [ "draft", "active" ])
  end

  def completed_case_plans
    case_plans.where(status: "completed")
  end

  def case_summary
    {
      id: id,
      beneficiary_name: beneficiary&.name,
      case_manager_name: case_manager&.name,
      status: status,
      approval_status: approval_status,
      duration_days: duration_in_days,
      case_plans_count: case_plans.count,
      services_count: services.count
    }
  end

  def can_be_closed?
    is_active? && case_plans.where(status: [ "draft", "active" ]).empty?
  end

  def close_case!
    return false unless can_be_closed?

    update!(status: "closed", closed_at: Time.current)
  end

  private

  def set_default_status
    self.status ||= "open"
  end

  def set_started_at
    self.started_at ||= Time.current
  end

  def set_closed_at
    if status == "closed" && closed_at.blank?
      self.closed_at = Time.current
    elsif status != "closed"
      self.closed_at = nil
    end
  end

  def started_at_not_in_future
    return unless started_at

    if started_at > Time.current
      errors.add(:started_at, "cannot be in the future")
    end
  end

  def closed_at_after_started_at
    return unless started_at && closed_at

    if closed_at < started_at
      errors.add(:closed_at, "cannot be before started date")
    end
  end

  def closed_at_required_for_closed_status
    if status == "closed" && closed_at.blank?
      errors.add(:closed_at, "is required when case is closed")
    end
  end
end
