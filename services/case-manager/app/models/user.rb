# frozen_string_literal: true

class User < AtharAuth::Models::User
  # Inherit all common attributes from AtharAuth::Models::User:
  # - id, name, email, global, project_id, user_type, scope, permissions
  # - global_user?, project_based_user?, can?(permission)
  # - from_token_data factory method with rich associations

  # CM-specific ActiveStruct associations
  has_many :managed_cases, class_name: "Case", foreign_key: :case_manager_id, primary_key: :id
  belongs_to :case_manager, foreign_key: :id, primary_key: :user_id, optional: true

  # CM-specific domain methods
  def accessible_cases
    if global_user?
      Case.all
    elsif supervisor?
      Case.where(project_id: project_id)
    elsif case_manager?
      managed_cases.where(project_id: project_id)
    else
      Case.none
    end
  end

  def can_manage_case?(case_obj)
    return true if global_user?
    return false unless case_manager

    case_obj.case_manager_id == case_manager.id &&
      (project_id.blank? || case_obj.project_id == project_id)
  end

  def accessible_beneficiaries
    if global_user?
      Beneficiary.all
    elsif supervisor?
      Beneficiary.joins(:cases).where(cases: { project_id: project_id })
    elsif case_manager?
      Beneficiary.joins(:cases).where(cases: { case_manager_id: id, project_id: project_id })
    else
      Beneficiary.none
    end
  end

  def can_manage_beneficiary?(beneficiary)
    return true if global_user?
    return true if supervisor? && beneficiary_in_accessible_projects?(beneficiary)
    return true if case_manager? && beneficiary_assigned_to_user?(beneficiary)
    false
  end

  # CM-specific role checks
  def case_manager?
    role&.name&.include?("case_manager")
  end

  def supervisor?
    role&.name&.include?("supervisor")
  end

  def social_worker?
    role&.name&.include?("social_worker")
  end

  def field_officer?
    role&.name&.include?("field_officer")
  end

  # Use inherited factory method from AtharAuth::Models::User
  # No need to override - from_token_data handles everything with rich associations

  private

  def beneficiary_in_accessible_projects?(beneficiary)
    return true if global_user?
    beneficiary.cases.exists?(project_id: project_id)
  end

  def beneficiary_assigned_to_user?(beneficiary)
    beneficiary.cases.exists?(case_manager_id: id, project_id: project_id)
  end
end
