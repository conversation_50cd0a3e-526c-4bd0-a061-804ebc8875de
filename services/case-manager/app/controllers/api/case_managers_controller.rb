module Api
  class CaseManagersController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # ✅ NEW: Use method call style authorization (like acts_as_approvable)
    authorize_resources

    api! "Lists all case managers"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all case managers.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :case_manager</code>.
    HTML
    )
    returns code: 200, desc: "List of case managers"

    def index
      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific case manager"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case manager"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific case manager by ID.
      Requires permission: <code>:read, :case_manager</code>.
    HTML
    )
    returns code: 200, desc: "Case manager details"

    def show
      serialize_response(@case_manager, params: { current_case_manager: current_case_manager })
    end

    api! "Creates a new case manager"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_manager, Hash, required: true, desc: "Case manager details" do
      param :user_id, String, required: true, desc: "User ID from auth system"
      param :supervisor_id, String, desc: "Supervisor's case manager ID"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new case manager record.
      Links a user from the auth system to the case management system.
      Requires permission: <code>:create, :case_manager</code>.
    HTML
    )
    returns code: 201, desc: "Case manager created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @case_manager = CaseManager.new(create_params)

      if @case_manager.save
        serialize_response(@case_manager, status: :created)
      else
        serialize_errors(@case_manager.errors)
      end
    end

    api :PUT, "/case_managers/:id", "Updates an existing case manager"
    api :PATCH, "/case_managers/:id", "Partially updates a case manager"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case manager"
    param :case_manager, Hash, required: true, desc: "Updated case manager fields" do
      param :supervisor_id, String, desc: "Supervisor's case manager ID"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing case manager's information.
      Note: user_id cannot be updated through this endpoint.
      Requires permission: <code>:update, :case_manager</code>.
    HTML
    )
    returns code: 200, desc: "Case manager updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @case_manager.update(update_params)
        serialize_response(@case_manager.reload)
      else
        serialize_errors(@case_manager.errors)
      end
    end

    api! "Deletes a case manager"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case manager"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a case manager by ID.
      Requires permission: <code>:destroy, :case_manager</code>.
    HTML
    )
    returns code: 204, desc: "Case manager deleted successfully"

    def destroy
      @case_manager.destroy!
      head :no_content
    end

    private

    def set_case_manager
      @case_manager = CaseManager.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Case manager not found" }, :not_found)
    end

    def create_params
      params.require(:case_manager).permit(
        :user_id,
        :supervisor_id
      )
    end

    def update_params
      params.fetch(:case_manager, {}).permit(
        :supervisor_id
      )
    end

    def authorize_read_all_or_own
      if can?(:read, :case_manager)
        @collection = CaseManager.all
      elsif can?(:read_own, :case_manager) && current_case_manager.present?
        @collection = CaseManager.where(id: current_case_manager.id)
      else
        render_forbidden("You don't have permission to view case managers")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :case_manager) ||
             (can?(:read_own, :case_manager) && is_own_resource?)
        render_forbidden("You don't have permission to view this case manager")
        false
      end
    end

    def authorize_create
      authorize!(:create, :case_manager)
    end

    def authorize_update
      unless can?(:update, :case_manager) ||
             (can?(:update_own, :case_manager) && is_own_resource?)
        render_forbidden("You don't have permission to update this case manager")
        false
      end
    end

    def authorize_destroy
      authorize!(:destroy, :case_manager)
    end

    def is_own_resource?
      @case_manager.id == current_case_manager&.id
    end

    def render_forbidden(message)
      serialize_errors({ detail: message }, :forbidden)
    end
  end
end
