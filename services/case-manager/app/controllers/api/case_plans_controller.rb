module Api
  class CasePlansController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # ✅ NEW: Use method call style authorization (like acts_as_approvable)
    authorize_resources

    api! "Lists all case plans"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all case plans.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :case_plan</code>.
    HTML
    )
    returns code: 200, desc: "List of case plans"

    def index
      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific case plan"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case plan"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific case plan by ID.
      Requires permission: <code>:read, :case_plan</code>.
    HTML
    )
    returns code: 200, desc: "Case plan details"

    def show
      serialize_response(@case_plan)
    end

    api! "Creates a new case plan"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_plan, Hash, required: true, desc: "Case plan details" do
      param :case_id, String, required: true, desc: "ID of the associated case"
      param :status, String, required: true, desc: "Plan status (draft, active, completed, cancelled)"
      param :summary, String, required: true, desc: "Summary of the case plan"
      param :approved, :boolean, desc: "Whether the plan is approved"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new case plan record.
      Case plans define the intervention strategy for a case.
      Requires permission: <code>:create, :case_plan</code>.
    HTML
    )
    returns code: 201, desc: "Case plan created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @case_plan = CasePlan.new(create_params)

      if @case_plan.save
        serialize_response(@case_plan, status: :created)
      else
        serialize_errors(@case_plan.errors)
      end
    end

    api :PUT, "/case_plans/:id", "Updates an existing case plan"
    api :PATCH, "/case_plans/:id", "Partially updates a case plan"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case plan"
    param :case_plan, Hash, required: true, desc: "Updated case plan fields" do
      param :status, String, desc: "Plan status (draft, active, completed, cancelled)"
      param :summary, String, desc: "Summary of the case plan"
      param :approved, :boolean, desc: "Whether the plan is approved"
      param :approved_at, DateTime, desc: "Approval date and time"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing case plan's information.
      Note: case_id cannot be updated through this endpoint.
      Requires permission: <code>:update, :case_plan</code>.
    HTML
    )
    returns code: 200, desc: "Case plan updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @case_plan.update(update_params)
        serialize_response(@case_plan.reload)
      else
        serialize_errors(@case_plan.errors)
      end
    end

    api! "Deletes a case plan"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case plan"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a case plan by ID.
      Requires permission: <code>:destroy, :case_plan</code>.
    HTML
    )
    returns code: 204, desc: "Case plan deleted successfully"

    def destroy
      @case_plan.destroy!
      head :no_content
    end

    private

    def set_case_plan
      @case_plan = CasePlan.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Case plan not found" }, :not_found)
    end

    def create_params
      params.require(:case_plan).permit(
        :case_id,
        :status,
        :summary,
        :approved
      )
    end

    def update_params
      params.fetch(:case_plan, {}).permit(
        :status,
        :summary,
        :approved,
        :approved_at
      )
    end

    def authorize_read_all_or_own
      if can?(:read, :case_plan)
        @collection = CasePlan.all
      elsif can?(:read_own, :case_plan) && current_case_manager.present?
        # Get case plans for cases managed by current case manager
        case_ids = Case.where(case_manager_id: current_case_manager.id).pluck(:id)
        @collection = CasePlan.where(case_id: case_ids)
      else
        render_forbidden("You don't have permission to view case plans")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :case_plan) ||
             (can?(:read_own, :case_plan) && is_own_resource?)
        render_forbidden("You don't have permission to view this case plan")
        false
      end
    end

    def authorize_create
      authorize!(:create, :case_plan)
    end

    def authorize_update
      unless can?(:update, :case_plan) ||
             (can?(:update_own, :case_plan) && is_own_resource?)
        render_forbidden("You don't have permission to update this case plan")
        false
      end
    end

    def authorize_destroy
      authorize!(:destroy, :case_plan)
    end

    def is_own_resource?
      return false unless current_case_manager.present?

      # Check if the case plan belongs to a case managed by current case manager
      case_record = Case.find_by(id: @case_plan.case_id)
      case_record&.case_manager_id == current_case_manager.id
    end

    def render_forbidden(message)
      serialize_errors({ detail: message }, :forbidden)
    end
  end
end
