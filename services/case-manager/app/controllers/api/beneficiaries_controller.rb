module Api
  class BeneficiariesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    authorize_resources

    api! "Lists all beneficiaries"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param "page[number]", Integer, desc: "Page number (default: 1)"
    param "page[size]", Integer, desc: "Number of items per page (default: 25, max: 100)"
    param "filter[gender]", String, desc: "Filter by gender (male, female, other)"
    param "filter[nationality]", String, desc: "Filter by nationality"
    param "filter[city]", String, desc: "Filter by city"
    param "sort", String, desc: "Sort by field name. Prefix with '-' for descending order"
    param "include", String, desc: "Include related resources (cases, case_managers, case_plans)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all beneficiaries.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :beneficiary</code>.
    HTML
    )
    returns code: 200, desc: "List of beneficiaries"

    def index
      Rails.logger.info "DEBUG: @beneficiaries = #{@beneficiaries.inspect}"
      Rails.logger.info "DEBUG: @beneficiaries.class = #{@beneficiaries.class}"
      Rails.logger.info "DEBUG: @beneficiaries.count = #{@beneficiaries.count}"

      apply_filters(@beneficiaries) do |filtered_and_sorted|
        Rails.logger.info "DEBUG: filtered_and_sorted = #{filtered_and_sorted.inspect}"
        Rails.logger.info "DEBUG: filtered_and_sorted.count = #{filtered_and_sorted.count}"

        records, meta = paginate(filtered_and_sorted)
        Rails.logger.info "DEBUG: records = #{records.inspect}"
        Rails.logger.info "DEBUG: meta = #{meta.inspect}"

        result = serialize_response(records, meta: meta)
        Rails.logger.info "DEBUG: serialize_response result = #{result.inspect}"
        result
      end
    end

    api! "Retrieves a specific beneficiary"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the beneficiary"
    param "include", String, desc: "Include related resources (cases, case_managers, case_plans)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific beneficiary by ID.
      Requires permission: <code>:read, :beneficiary</code>.
    HTML
    )
    returns code: 200, desc: "Beneficiary details"

    def show
      serialize_response(@beneficiary)
    end

    api! "Creates a new beneficiary"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :beneficiary, Hash, required: true, desc: "Beneficiary details" do
      param :name, String, required: true, desc: "Full name of the beneficiary"
      param :gender, String, required: true, desc: "Gender (male, female, other)"
      param :nationality, String, required: true, desc: "Nationality"
      param :city, String, required: true, desc: "City of residence"
      param :date_of_birth, Date, required: true, desc: "Date of birth"
    end
    param "include", String, desc: "Include related resources"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new beneficiary record.
      Requires permission: <code>:create, :beneficiary</code>.
    HTML
    )
    returns code: 201, desc: "Beneficiary created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @beneficiary = Beneficiary.new(create_params)
      @beneficiary.project_id = current_project&.id if current_project

      if @beneficiary.save
        serialize_response(@beneficiary, status: :created)
      else
        serialize_errors(@beneficiary.errors)
      end
    end

    api :PUT, "/beneficiaries/:id", "Updates an existing beneficiary"
    api :PATCH, "/beneficiaries/:id", "Partially updates a beneficiary"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the beneficiary"
    param :beneficiary, Hash, required: true, desc: "Updated beneficiary fields" do
      param :name, String, desc: "Full name of the beneficiary"
      param :gender, String, desc: "Gender (male, female, other)"
      param :nationality, String, desc: "Nationality"
      param :city, String, desc: "City of residence"
      param :date_of_birth, Date, desc: "Date of birth"
    end
    param "include", String, desc: "Include related resources"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing beneficiary's information.
      At least one field should be provided in the beneficiary parameter.
      Requires permission: <code>:update, :beneficiary</code>.
    HTML
    )
    returns code: 200, desc: "Beneficiary updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @beneficiary.update(update_params)
        serialize_response(@beneficiary.reload)
      else
        serialize_errors(@beneficiary.errors)
      end
    end

    api! "Deletes a beneficiary"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the beneficiary"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a beneficiary by ID.
      Requires permission: <code>:destroy, :beneficiary</code>.
    HTML
    )
    returns code: 204, desc: "Beneficiary deleted successfully"

    def destroy
      @beneficiary.destroy!
      head :no_content
    end

    private

    def set_beneficiary
      @beneficiary = Beneficiary.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Beneficiary not found" }, :not_found)
    end

    def create_params
      params.require(:beneficiary).permit(
        :name,
        :gender,
        :nationality,
        :city,
        :date_of_birth
      )
    end

    def update_params
      params.fetch(:beneficiary, {}).permit(
        :name,
        :gender,
        :nationality,
        :city,
        :date_of_birth
      )
    end

    def authorize_read_all
      @collection = Beneficiary.all
      authorize!(:read, :beneficiary)
    end

    def authorize_read_specific
      authorize!(:read, :beneficiary)
    end

    def authorize_create
      authorize!(:create, :beneficiary)
    end

    def authorize_update
      authorize!(:update, :beneficiary)
    end

    def authorize_destroy
      authorize!(:destroy, :beneficiary)
    end
  end
end
