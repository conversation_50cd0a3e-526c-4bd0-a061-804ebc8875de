module Api
  class CasesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # ✅ NEW: Use method call style authorization (like acts_as_approvable)
    authorize_resources

    api! "Lists all cases"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all cases.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "List of cases"

    def index
      # ✅ @cases is automatically prepared by authorize_resources with project filtering
      apply_filters(@cases) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific case by ID.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case details"

    def show
      # ✅ @case is automatically prepared by authorize_resources with project validation
      serialize_response(@case)
    end

    api! "Creates a new case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case, Hash, required: true, desc: "Case details" do
      param :beneficiary_id, String, required: true, desc: "ID of the beneficiary"
      param :case_manager_id, String, required: true, desc: "ID of the assigned case manager"
      param :status, String, required: true, desc: "Case status (open, in_progress, closed, suspended)"
      param :approval_status, String, desc: "Approval status (pending, approved, rejected)"
      param :started_at, DateTime, desc: "Case start date and time"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new case record.
      Links a beneficiary with a case manager.
      Requires permission: <code>:create, :case</code>.
    HTML
    )
    returns code: 201, desc: "Case created successfully"
    error code: 422, desc: "Validation errors"

    def create
      # ✅ Authorization checked by authorize_resources, now create new case
      @case = Case.new(create_params)
      @case.project_id = current_project&.id if current_project

      if @case.save
        serialize_response(@case, status: :created)
      else
        serialize_errors(@case.errors)
      end
    end

    api :PUT, "/cases/:id", "Updates an existing case"
    api :PATCH, "/cases/:id", "Partially updates a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    param :case, Hash, required: true, desc: "Updated case fields" do
      param :case_manager_id, String, desc: "ID of the assigned case manager"
      param :status, String, desc: "Case status (open, in_progress, closed, suspended)"
      param :approval_status, String, desc: "Approval status (pending, approved, rejected)"
      param :started_at, DateTime, desc: "Case start date and time"
      param :closed_at, DateTime, desc: "Case closure date and time"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing case's information.
      Note: beneficiary_id cannot be updated through this endpoint.
      Requires permission: <code>:update, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      # ✅ @case is automatically prepared by authorize_resources with project validation
      if @case.update(update_params)
        serialize_response(@case.reload)
      else
        serialize_errors(@case.errors)
      end
    end

    api! "Deletes a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a case by ID.
      Requires permission: <code>:destroy, :case</code>.
    HTML
    )
    returns code: 204, desc: "Case deleted successfully"

    def destroy
      # ✅ @case is automatically prepared by authorize_resources with project validation
      @case.destroy!
      head :no_content
    end

    private

    # ✅ REMOVED: set_case method no longer needed - authorize_resources handles resource loading

    def create_params
      params.require(:case).permit(
        :beneficiary_id,
        :case_manager_id,
        :status,
        :approval_status,
        :started_at
      )
    end

    def update_params
      params.fetch(:case, {}).permit(
        :case_manager_id,
        :status,
        :approval_status,
        :started_at,
        :closed_at
      )
    end

    # ✅ REMOVED: All authorization methods replaced by authorize_resources
    # The authorize_resources method automatically handles:
    # - Permission checking (can?(:action, :case))
    # - Project-based filtering for collections
    # - Resource loading with project validation
    # - Standard error responses (401/403)
    # - Instance variable preparation (@cases, @case)
  end
end
