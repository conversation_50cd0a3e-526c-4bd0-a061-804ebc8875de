class CaseSerializer
  include JSONAPI::Serializer

  attributes :beneficiary_id, :case_manager_id, :status, :approval_status,
             :started_at, :closed_at, :created_at, :updated_at

  # Computed attributes
  attribute :duration_in_days do |case_record|
    case_record.duration_in_days
  end

  attribute :is_active do |case_record|
    case_record.is_active?
  end

  attribute :is_closed do |case_record|
    case_record.is_closed?
  end

  attribute :is_suspended do |case_record|
    case_record.is_suspended?
  end

  attribute :requires_approval do |case_record|
    case_record.requires_approval?
  end

  attribute :is_approved do |case_record|
    case_record.is_approved?
  end

  attribute :is_rejected do |case_record|
    case_record.is_rejected?
  end

  attribute :can_be_closed do |case_record|
    case_record.can_be_closed?
  end

  # Relationships
  belongs_to :beneficiary
  belongs_to :case_manager
  has_many :case_plans
  has_many :services, through: :case_plans

  # Meta information
  meta do |case_record, params|
    {
      case_summary: case_record.case_summary
    }
  end
end
