class CaseManagerSerializer
  include JSONAPI::Serializer

  attributes :user_id, :supervisor_id, :created_at, :updated_at

  # User data attributes from gRPC
  attribute :name do |case_manager|
    case_manager.name
  end

  attribute :email do |case_manager|
    case_manager.email
  end

  attribute :phone do |case_manager|
    case_manager.phone
  end

  # Computed attributes
  attribute :active_cases_count do |case_manager|
    case_manager.active_cases_count
  end

  attribute :total_cases_count do |case_manager|
    case_manager.total_cases_count
  end

  attribute :is_supervisor do |case_manager|
    case_manager.is_supervisor?
  end

  # Relationships
  has_many :cases
  has_many :beneficiaries, through: :cases
  has_many :case_plans, through: :cases
  belongs_to :supervisor, record_type: :case_manager, serializer: :case_manager
  has_many :supervised_case_managers, record_type: :case_manager, serializer: :case_manager

  # Conditional attributes based on permissions
  attribute :user_roles, if: proc { |record, params|
    params && params[:current_case_manager] &&
    (params[:current_case_manager].id == record.id || params[:can_view_roles])
  } do |case_manager|
    case_manager.user_data&.roles || []
  end

  attribute :permissions, if: proc { |record, params|
    params && params[:current_case_manager] &&
    (params[:current_case_manager].id == record.id || params[:can_view_permissions])
  } do |case_manager|
    case_manager.user_data&.permissions || []
  end
end
