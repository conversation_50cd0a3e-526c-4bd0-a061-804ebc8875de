class CasePlanSerializer
  include JSONAPI::Serializer

  attributes :case_id, :status, :summary, :approved, :approved_at, :created_at, :updated_at

  # Computed attributes
  attribute :is_draft do |case_plan|
    case_plan.is_draft?
  end

  attribute :is_active do |case_plan|
    case_plan.is_active?
  end

  attribute :is_completed do |case_plan|
    case_plan.is_completed?
  end

  attribute :is_cancelled do |case_plan|
    case_plan.is_cancelled?
  end

  attribute :is_approved do |case_plan|
    case_plan.is_approved?
  end

  attribute :is_pending_approval do |case_plan|
    case_plan.is_pending_approval?
  end

  attribute :can_be_activated do |case_plan|
    case_plan.can_be_activated?
  end

  attribute :can_be_completed do |case_plan|
    case_plan.can_be_completed?
  end

  attribute :services_count do |case_plan|
    case_plan.services_count
  end

  attribute :completed_services_count do |case_plan|
    case_plan.completed_services_count
  end

  attribute :progress_percentage do |case_plan|
    case_plan.progress_percentage
  end

  # Relationships
  belongs_to :case
  has_many :services
  has_one :beneficiary, through: :case
  has_one :case_manager, through: :case

  # Meta information
  meta do |case_plan, params|
    {
      plan_summary: case_plan.plan_summary
    }
  end
end
