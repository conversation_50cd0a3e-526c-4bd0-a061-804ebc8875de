class BeneficiarySerializer
  include JSONAPI::Serializer

  attributes :name, :gender, :nationality, :city, :date_of_birth, :created_at, :updated_at

  # Computed attributes
  attribute :age do |beneficiary|
    beneficiary.age
  end

  attribute :is_minor do |beneficiary|
    beneficiary.is_minor?
  end

  attribute :is_adult do |beneficiary|
    beneficiary.is_adult?
  end

  attribute :active_cases_count do |beneficiary|
    beneficiary.active_cases_count
  end

  attribute :total_cases_count do |beneficiary|
    beneficiary.total_cases_count
  end

  # Relationships
  has_many :cases
  has_many :case_managers, through: :cases
  has_many :case_plans, through: :cases

  # Meta information
  meta do |beneficiary, params|
    {
      full_profile: beneficiary.full_profile
    }
  end
end
