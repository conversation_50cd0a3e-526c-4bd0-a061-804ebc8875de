class ServiceSerializer
  include JSONAPI::Serializer

  attributes :name, :description, :category, :provider_type, :case_plan_id, :created_at, :updated_at

  # Computed attributes
  attribute :is_internal do |service|
    service.is_internal?
  end

  attribute :is_external do |service|
    service.is_external?
  end

  attribute :is_partner do |service|
    service.is_partner?
  end

  attribute :is_health_service do |service|
    service.is_health_service?
  end

  attribute :is_education_service do |service|
    service.is_education_service?
  end

  attribute :is_legal_service do |service|
    service.is_legal_service?
  end

  attribute :is_social_service do |service|
    service.is_social_service?
  end

  attribute :is_financial_service do |service|
    service.is_financial_service?
  end

  attribute :is_housing_service do |service|
    service.is_housing_service?
  end

  attribute :is_employment_service do |service|
    service.is_employment_service?
  end

  # Relationships
  belongs_to :case_plan
  has_one :case, through: :case_plan
  has_one :beneficiary, through: :case
  has_one :case_manager, through: :case

  # Meta information
  meta do |service, params|
    {
      service_summary: service.service_summary,
      full_details: service.full_details
    }
  end
end
