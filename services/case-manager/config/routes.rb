Rails.application.routes.draw do
  apipie

  namespace :api do
    resources :beneficiaries
    resources :case_managers
    resources :services
    resources :cases
    resources :case_plans

    # Mount shared session endpoints from auth gem
    mount AtharAuth::Engine, at: "session"
  end

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Sidekiq Web UI
  require "sidekiq/web"

  # Make sure sidekiq-scheduler web UI is loaded
  begin
    require "sidekiq-scheduler/web"
  rescue LoadError
    # Handle the case where sidekiq-scheduler is not available
    puts "WARNING: sidekiq-scheduler/web could not be loaded"
  end

  # Secure the Sidekiq Web UI with basic auth in production
  if Rails.env.production?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      # Replace with a real check in production
      ActiveSupport::SecurityUtils.secure_compare(username, ENV["SIDEKIQ_USERNAME"]) &
        ActiveSupport::SecurityUtils.secure_compare(password, ENV["SIDEKIQ_PASSWORD"])
    end
  end

  mount Sidekiq::Web => "/sidekiq"

  # Defines the root path route ("/")
  root "application#index"
end
