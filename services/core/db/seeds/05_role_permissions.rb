# Define common permissions that all roles should have
# Note: Salary permissions are handled separately in 08_salary_permissions.rb
common_permissions = Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people")
                              .or(Permission.where(action: "read", subject_class: "Role"))
                              .or(Permission.where(action: "read", subject_class: "Project"))
                              .or(Permission.where(action: "read_own", subject_class: "User", system_name: "core"))
                              .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                              .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                              .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))

# Assign permissions to roles
role_permission_map = {
  "super_admin" => Permission.all,
  "admin" => Permission.all.where.not(action: %w[manage cancel_own read_own update_own]),
  "hr_manager" => Permission.where(action: %w[read create update], subject_class: "Employee")
                            .or(Permission.where(subject_class: "Leave"))
                            .or(Permission.where(subject_class: "AttendanceEvent"))
                            .or(Permission.where(subject_class: "AttendanceDevice"))
                            .or(Permission.where(subject_class: "AttendanceSyncLog"))
                            .or(Permission.where(subject_class: "AttendanceExemption"))
                            .or(Permission.where(action: %w[read update create], subject_class: "User"))
                            .or(Permission.where(subject_class: "setting"))
                            .or(Permission.where(subject_class: "ApprovalRequest", system_name: "people")),
  "hr_officer" => Permission.where(system_name: "people", action: %w[read update create approve reject])
                            .or(Permission.where(action: %w[read], subject_class: "User"))
                            .or(Permission.where(action: %w[read create update approve reject], subject_class: "AttendanceEvent"))
                            .or(Permission.where(action: %w[read sync test_connection execute_command], subject_class: "AttendanceDevice"))
                            .or(Permission.where(action: %w[read], subject_class: "AttendanceSyncLog"))
                            .or(Permission.where(action: %w[read], subject_class: "setting"))
                            .or(Permission.where(subject_class: "ApprovalRequest", system_name: "people", action: %w[read approve reject])),
  "procurement_manager" => Permission.where(system_name: "procure").or(Permission.where(action: %w[read], subject_class: "User"))
                                     .or(Permission.where(subject_class: "ApprovalRequest", system_name: "procure")),
  "procurement_officer" => Permission.where(system_name: "procure", action: %w[read create update]).or(Permission.where(action: %w[read], subject_class: "User"))
                                     .or(Permission.where(subject_class: "ApprovalRequest", system_name: "procure", action: %w[read approve reject])),
  "financial_manager" => Permission.where(subject_class: "Payroll").or(Permission.where(action: %w[read], subject_class: "User"))
                                   .or(Permission.where(subject_class: "ApprovalRequest")),
  "accountant" => Permission.where(subject_class: "Payroll", action: %w[read update]).or(Permission.where(action: %w[read], subject_class: "User"))
                            .or(Permission.where(subject_class: "ApprovalRequest", action: %w[read approve reject])),
  "case_manager" => Permission.where(system_name: "cm").or(Permission.where(action: %w[read], subject_class: "User"))
                              .or(Permission.where(subject_class: "ApprovalRequest", system_name: "cm")),
  "project_manager" => Permission.where(action: %w[read_project update_project manage_project], subject_class: "Employee", system_name: "people")
                                 .or(Permission.where(action: %w[read_project approve_project reject_project], subject_class: "Leave", system_name: "people"))
                                 .or(Permission.where(action: %w[read_project update_project], subject_class: "AttendanceEvent", system_name: "people"))
                                 .or(Permission.where(action: %w[read generate export]).where.not(subject_class: "Employee", system_name: "people"))
                                 .or(Permission.where(action: %w[read], subject_class: "User"))
                                 .or(Permission.where(subject_class: "ApprovalRequest", action: %w[read approve reject])),
  "supervisor" => Permission.where(action: %w[read_project update_project], subject_class: "Employee", system_name: "people")
                            .or(Permission.where(action: %w[read_project approve_project reject_project], subject_class: "Leave", system_name: "people"))
                            .or(Permission.where(action: %w[read_project update_project], subject_class: "AttendanceEvent", system_name: "people"))
                            .or(Permission.where(action: "read").where.not(subject_class: "Employee", system_name: "people"))
                            .or(Permission.where(action: %w[read], subject_class: "User"))
                            .or(Permission.where(subject_class: "ApprovalRequest", action: %w[read approve reject])),
  "psychologist" => Permission.where(system_name: "cm").or(Permission.where(action: %w[read], subject_class: "User"))
                              .or(Permission.where(subject_class: "ApprovalRequest", system_name: "cm")),
  "social_media_specialist" => Permission.where(action: %w[read], subject_class: "User")
                                         .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                                         .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                                         .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                                         .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest")),
  "employee" => Permission.where(action: %w[read], subject_class: "User")
                          .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                          .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                          .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                          .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))
}

# Define basic employee permissions for roles that should inherit employee permissions
basic_employee_permissions = Permission.where(action: %w[read], subject_class: "User")
                                       .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                                       .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                                       .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                                       .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))

# Define project-based employee permissions for roles that should see employees in their projects
project_employee_permissions = Permission.where(action: %w[read_project update_project], subject_class: "Employee", system_name: "people")
                                         .or(Permission.where(action: %w[read_project approve_project reject_project], subject_class: "Leave", system_name: "people"))
                                         .or(Permission.where(action: %w[read_project], subject_class: "AttendanceEvent", system_name: "people"))
                                         .or(basic_employee_permissions)

# Roles with basic employee permissions (no employee list access)
basic_employee_roles = %w[
  coach
  graphic_designer
  community_mobilizer
  club_facilitator
  community_animator
  housekeeping
  fundraising_officer
  volunteer
]

# Roles with project-based employee permissions (can see employees in their projects)
project_employee_roles = %w[
  program_manager
]

# Add basic employee permissions to the role permission map
basic_employee_roles.each do |role_name|
  role_permission_map[role_name] = basic_employee_permissions
end

# Add project-based employee permissions to the role permission map
project_employee_roles.each do |role_name|
  role_permission_map[role_name] = project_employee_permissions
end

role_permission_map.each do |role_name, perms|
  role = Role.find_by!(name: role_name) # Fail if role doesn't exist

  # Add role-specific permissions
  perms.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end

  # Add common permissions to all roles except super_admin and admin (who already have all permissions)
  unless %w[super_admin admin].include?(role_name)
    common_permissions.each do |permission|
      unless role.permissions.exists?(permission.id)
        role.permissions << permission
      end
    end
  end
end

puts "✅ Role permissions mapped successfully!"
