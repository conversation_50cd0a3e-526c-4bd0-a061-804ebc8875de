# frozen_string_literal: true

module AtharAuth
  module ResourceAuthorization
    extend ActiveSupport::Concern

    class_methods do
      # Method call style (like acts_as_approvable)
      def authorize_resources(options = {})
        # Store configuration
        class_attribute :authorization_config, default: options

        # Set up before_actions based on configuration
        setup_authorization_callbacks(options)
      end

      private

      def setup_authorization_callbacks(options)
        # Default actions that get auto-authorization
        auto_actions = options[:only] || [:index, :show, :create, :update, :destroy]
        collection_actions = options[:collection_actions] || [:index]
        skip_actions = options[:except] || []

        # Remove skipped actions
        auto_actions -= skip_actions
        collection_actions -= skip_actions

        # Set up callbacks
        before_action :authorize_and_load_collection!, only: collection_actions
        before_action :authorize_and_load_resource!, only: (auto_actions - collection_actions)
      end
    end

    private

    def authorize_and_load_collection!
      resource_class = controller_name.classify.constantize
      options = {}
      collection = authorize_collection(:index, resource_class, options)

      # Set instance variable with resource name (like @cases, @employees)
      instance_variable_set("@#{controller_name}", collection)
    end

    def authorize_and_load_resource!
      resource_class = controller_name.classify.constantize
      options = {}
      resource = authorize_single_resource(action_name.to_sym, resource_class, options)

      # Set instance variable with singular resource name (like @case, @employee)
      instance_variable_set("@#{controller_name.singularize}", resource)
    end
  end
end
