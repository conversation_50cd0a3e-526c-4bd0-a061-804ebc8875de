# frozen_string_literal: true

module Athar
  module Commons
    class Engine < ::Rails::Engine
      isolate_namespace Athar::Commons

      # This allows Rails to autoload the files in the app directory
      config.to_prepare do
        # Any initialization code needed
      end

      # Add all directories to the autoload paths
      initializer "athar.commons.autoload", before: :set_autoload_paths do |app|
        puts "Athar Commons autoload initializer is running"

        # Get the gem's root path
        root_path = Athar::Commons::Engine.root

        # Add paths to autoload_paths directly
        app.config.autoload_paths += [
          "#{root_path}/app/controllers",
          "#{root_path}/app/controllers/concerns",
          "#{root_path}/app/models",
          "#{root_path}/app/models/concerns",
          "#{root_path}/app/services",
          "#{root_path}/app/validators"
        ]

        # For eager loading
        app.config.eager_load_paths += [
          "#{root_path}/app/controllers",
          "#{root_path}/app/controllers/concerns",
          "#{root_path}/app/models",
          "#{root_path}/app/models/concerns",
          "#{root_path}/app/services",
          "#{root_path}/app/validators"
        ]
      end

      # Extend ActiveRecord with our extensions
      # initializer "athar.commons.extend_active_record" do
      #   ActiveSupport.on_load(:active_record) do
      #
      #     ActiveRecord::Base.include Athar::Commons::Models::Concerns::Ransackable
      #     ActiveRecord::Base.include Athar::Commons::Models::ActiveRecordExtensions
      #   end
      # end

      # Configure generators
      config.generators do |g|
        g.test_framework :rspec
      end
    end
  end
end
